<app-header [title]="translate('EXPENSE')"></app-header>



<ion-content >
  <ion-item>
    <ion-button id="add-expense-button"  *ngIf="displayBottomAdd"
      (click)="addExpense()">{{translate("ADD_EXPENSE_NOTE")}}</ion-button>
  </ion-item>
  <!-- Formulaire d'update -->
  <form id="expense-form" [formGroup]="expenseForm" 
    *ngIf="displayForm">
    <ion-item class="center-datetime">
      <ion-input type="date" formControlName="expenseDate" required></ion-input>
    </ion-item>
    <div>
      <ion-item>
        <ion-select formControlName="typeName" [placeholder]="translate('TYPE_OF_EXPENSE_REPORT')"
          [(ngModel)]="selectedType" (ionChange)="typeChange($event)">
          <ion-select-option *ngFor="let type of types" [value]="type">{{ type.name }} </ion-select-option>
        </ion-select>
      </ion-item>
      <ion-note *ngIf="expenseForm.get('typeName')?.touched && expenseForm.get('typeName')?.hasError('required')"
        color="danger">{{ translate('TYPE_REQUIRED') }}</ion-note>
      <ion-item>
        <ion-input type="number" formControlName="amount" [placeholder]="translate('AMOUNT')"></ion-input>
      </ion-item>
      <ion-note *ngIf="expenseForm.get('amount')?.touched && expenseForm.get('amount')?.hasError('required')"
        color="danger">{{ translate('AMOUNT_REQUIRED') }}</ion-note>
      <ion-item>
        <ion-textarea id="expense-description-textarea" formControlName="description" maxlength="255"
          [placeholder]="translate('DESCRIPTION')"></ion-textarea>
      </ion-item>
      <ion-item>
        <ion-input type="file" accept="image/*" (change)="encodeImageFile($event)" required></ion-input>
      </ion-item>
      
      <ion-item *ngIf="expenseForm.value.attachementBase64">
        <img [src]="expenseForm.value.attachementBase64" alt="Uploaded Image" />
      </ion-item>
      
      <ion-item>
        <ion-button (click)="takePhotoForExpense()">{{ translate('TAKE_PICTURE') }}</ion-button>
      </ion-item>
      <ion-item *ngIf="attachementBase64">
        <img [src]="attachementBase64" style="max-height:200px;margin: auto;" />
      </ion-item>
      <!-- save -->
      <ion-button id="save-expense-button"  expand="full" type="submit" [disabled]="!expenseForm.valid" *ngIf="!isUpdate"
        (click)="saveExpense()">{{ translate('SAVE') }}</ion-button>
        
       <ion-button  id="save-updated-expense-button"  expand="full" type="submit" [disabled]="!expenseForm.valid" *ngIf="isUpdate" 
        (click)="saveUpdatedExpense()">{{ translate('SAVE') }} up</ion-button>
      <ion-button id="cancel-expense-button" expand="full" (click)="cancel()">{{ translate('CANCEL') }}</ion-button>
    </div>
  </form>
  <ion-row>
    <ion-col>
      <ion-label>
        <p>{{translate("START_DATE")}}</p>
      </ion-label>
      <ion-input [(ngModel)]="firstDate" type="date" (ionChange)="getExpenseByDates()"></ion-input>
    </ion-col>
    <ion-col>
      <ion-label>
        <p>{{translate("END_DATE")}}</p>
      </ion-label>
      <ion-input [(ngModel)]="lastDate" type="date" (ionChange)="getExpenseByDates()"></ion-input>
    </ion-col>
  </ion-row>
  <h1 class="titre_h1">{{translate('TOTAL')}} : {{total}}</h1>
  <!-- Expense Cards -->
  <div id="expense-cards">
    <ion-card *ngFor="let expense of expenses" >
      <ion-card-header >
        <ion-card-title>{{ expense.expenseDate | date: 'dd/MM/yyyy' }}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p><strong>{{ translate('EXPENSE_REPORT_TYPE') }}:</strong> {{
          getExpenseTypeNameFromListById(expense.expenseTypeId) }}</p>
        <p><strong>{{ translate('AMOUNT') }}:</strong> {{ expense.montant }}</p>
        <p><strong>{{ translate('DESCRIPTION') }}:</strong> {{ expense.description }}</p>
        <p><strong>{{ translate('STATUS') }}:</strong> {{ expense.status }}</p>
        <p><strong>{{ translate('ACTIVTY') }}:</strong> {{ getActivityTypeNameFromListById(expense.activityId) }}</p>
        <div class="action-buttons">
          <ion-button
            id="update-expense-button-{{ expense.id }}"
            *ngIf="expense.status == 'Nouveau'"
            (click)="updateExpense(expense)">
            <ion-icon name="create-outline"></ion-icon>
          </ion-button>
        
          <ion-button
            id="delete-expense-button-{{ expense.id }}"
            *ngIf="expense.status == 'Nouveau'"
            (click)="deleteExpense(expense.id)">
            <ion-icon name="trash-outline"></ion-icon>
          </ion-button>
        </div>
        
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>