export const TablesUpgrades  = [
    
       { toVersion: 1,
        statements: [
            `CREATE TABLE IF NOT EXISTS planning_validation (
                id INTEGER PRIMARY KEY, 
                notes TEXT, 
                status TEXT, 
                planning_validation_date INTEGER, 
                synchronized INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS prospect_order_prediction (
                id INTEGER PRIMARY KEY, 
                prediction_date INTEGER, 
                prospect_id INTEGER, 
                product_id INTEGER, 
                order_quantity INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS planning (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                planning_date INTEGER, 
                prospect_id INTEGER, 
                synchronized INTEGER, 
                status TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS marketing_action (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT, 
                budget INTEGER, 
                product_id TEXT, 
                prospect_id TEXT, 
                marketingAction_date INTEGER, 
                synchronized INTEGER, 
                status TEXT, 
                description TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS geolocation (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                pos_timestamp INTEGER, 
                latitude TEXT, 
                longitude TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS opportunitynote (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                expense_timestamp INTEGER, 
                name TEXT, 
                budget INTEGER, 
                product_id TEXT, 
                prospect_id TEXT, 
                pharmacie_id TEXT, 
                synchronized INTEGER, 
                status INTEGER, 
                description TEXT, 
                attachementBase64 TEXT, 
                attachmentName TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS prospect (
                id INTEGER PRIMARY KEY, 
                firstname TEXT, 
                lastname TEXT, 
                activity TEXT, 
                potential INTEGER, 
                address TEXT, 
                gsm TEXT, 
                phone TEXT, 
                email TEXT, 
                note TEXT, 
                secretary TEXT, 
                grade TEXT, 
                speciality_id INTEGER, 
                sector_id INTEGER, 
                locality_id INTEGER, 
                lat DOUBLE, 
                lng DOUBLE, 
                map_address TEXT, 
                status TEXT, 
                synchronized BOOLEAN, 
                validation INTEGER, 
                type_id INTEGER, 
                establishment_id INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS visit (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                visit_date INTEGER, 
                prospect_id INTEGER, 
                general_note TEXT, 
                patient_number INTEGER, 
                gadget_id INTEGER, 
                gadget_quantity INTEGER, 
                user_id INTEGER, 
                user_name TEXT, 
                companion_id INTEGER, 
                lat DOUBLE, 
                lng DOUBLE, 
                status TEXT, 
                synchronized BOOLEAN
            );`,
            `CREATE TABLE IF NOT EXISTS visit_product (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                visit_id INTEGER,
                purchase_order_id INTEGER,
                product_id INTEGER,
                comment TEXT,
                sample_quantity INTEGER,
                order_quantity INTEGER,
                rank INTEGER,
                smily INTEGER,
                sale_quantity INTEGER,
                urgent BOOLEAN,
                prescription_quantity INTEGER,
                freeOrder INTEGER,
                lab_gratuity TEXT,
                status TEXT,
                synchronized BOOLEAN
            );`,
            `CREATE TABLE IF NOT EXISTS wholesaler (
                id INTEGER PRIMARY KEY, 
                name TEXT, 
                responsible TEXT, 
                phone INTEGER, 
                address TEXT, 
                description TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS product (
                id INTEGER PRIMARY KEY, 
                ordre INTEGER, 
                name TEXT, 
                price NUMBER, 
                buying_price NUMBER, 
                version NUMBER, 
                quantity_unit TEXT, 
                package_quantity NUMBER, 
                stock NUMBER, 
                description TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS sector (
                id INTEGER PRIMARY KEY, 
                name TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS gadget (
                id INTEGER PRIMARY KEY, 
                name TEXT, 
                type TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS locality (
                id INTEGER PRIMARY KEY, 
                name TEXT, 
                sector_id INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS speciality (
                id INTEGER PRIMARY KEY, 
                name TEXT, 
                action INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS user (
                user_id INTEGER PRIMARY KEY, 
                username TEXT, 
                password TEXT, 
                first_last_name TEXT, 
                work_type TEXT, 
                working_days INTEGER, 
                last_synchronisation TEXT, 
                last_receive_date INTEGER, 
                first_sync BOOLEAN, 
                time TEXT, 
                auto_sync BOOLEAN, 
                lock_after_sync BOOLEAN, 
                multi_wholesaler BOOLEAN, 
                sync_cycle NUMBER, 
                comments_dictionary TEXT, 
                open_report_period NUMBER, 
                open_expense_period NUMBER
            );`,
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY, 
                name TEXT, 
                delegate_id INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS expense (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                activity_id INTEGER, 
                expense_date INTEGER, 
                description TEXT, 
                montant FLOAT, 
                expense_type_id INTEGER, 
                attachement_base64 TEXT, 
                attachment_name TEXT, 
                status TEXT,
                synchronized INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS expense_type (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT, 
                amount NUMBER, 
                required_attachment BOOLEAN
            );`,
            `CREATE TABLE IF NOT EXISTS activity (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                activity_date INTEGER, 
                hour_number INTEGER, 
                activity_type_id INTEGER, 
                comment TEXT, 
                synchronized INTEGER, 
                status TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS activity_type (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS prospect_type (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS potential (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS potential_product (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                potential_id INTEGER, 
                product_id INTEGER, 
                prospect_id INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                notification_text TEXT, 
                notification_day INTEGER, 
                notification_month INTEGER, 
                notification_year INTEGER, 
                notification_hour INTEGER, 
                notification_minutes INTEGER, 
                status INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS purchase_order (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                visit_id INTEGER, 
                attachmentBase64 TEXT, 
                attachmentName TEXT, 
                placement_method TEXT, 
                wholesaler_id INTEGER, 
                purchase_order_template_id INTEGER, 
                generate_po BOOLEAN, 
                generate_do BOOLEAN, 
                synchronized INTEGER, 
                status TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS product_range (
                range_id INTEGER, 
                product_id INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS range (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS product_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT, 
                document_base64 TEXT, 
                product_id INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS charge_plan (
                speciality_id INTEGER, 
                product_id INTEGER, 
                rank INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS goal (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                item_order TEXT, 
                name TEXT, 
                first_date INTEGER, 
                last_date INTEGER, 
                type TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS goal_item (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                activity INTEGER, 
                potential_id INTEGER, 
                product_id INTEGER, 
                sector_id INTEGER, 
                speciality_id INTEGER, 
                prospect_type_id INTEGER, 
                value INTEGER, 
                goal_id INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS message_tag (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                visit_id INTEGER, 
                user_id INTEGER, 
                synchronized INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS recovery (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                date INTEGER, 
                payment TEXT, 
                amount NUMBER, 
                attachment_id INTEGER, 
                description TEXT, 
                purchase_order_id INTEGER, 
                status TEXT, 
                synchronized INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS attachment (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                attachmentBase64 TEXT, 
                attachmentName TEXT, 
                status TEXT, 
                synchronized INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS purchase_order_template (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT, 
                first_date INTEGER, 
                last_date INTEGER, 
                gifts_id TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS purchase_order_template_item (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                product_id INTEGER, 
                quantity INTEGER, 
                freeOrder INTEGER, 
                labGratuity INTEGER, 
                purchase_order_template_id INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS free_quantity_rule_item (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                product_id INTEGER, 
                potential_id INTEGER, 
                prospect_type_id INTEGER, 
                orderQuantity INTEGER, 
                freeQuantity INTEGER, 
                labGratuity INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS message (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                text TEXT, 
                type INTEGER, 
                date INTEGER, 
                user_id INTEGER, 
                status TEXT, 
                synchronized INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS configuration (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                database_version INTEGER, 
                labo_name TEXT, 
                server_url TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS establishment (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT, 
                activity TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS presentation_time_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                product_document_id INTEGER, 
                start_time INTEGER, 
                end_time INTEGER, 
                visit_id INTEGER, 
                synchronized INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS note (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                sector_ids TEXT, 
                speciality_ids TEXT, 
                activities TEXT, 
                note TEXT, 
                link TEXT
            );`,
          
          
        ]
            },
            {
                toVersion: 2,
                statements: [
                  `CREATE TABLE IF NOT EXISTS next_action_rule (
                            id INTEGER PRIMARY KEY AUTOINCREMENT, 
                            totalRevenue INTEGER, 
                            period INTEGER, 
                            action TEXT
                        );`,
                         `CREATE TABLE IF NOT EXISTS budget_allocation (
                            id INTEGER PRIMARY KEY AUTOINCREMENT, 
                            year INTEGER, 
                            monthlyBudget Float, 
                            type String
                        );`,
                        
                  `CREATE TABLE IF NOT EXISTS user_permissions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            permission_name TEXT NOT NULL,
                            user_id INTEGER,
                            created_at INTEGER DEFAULT (strftime('%s', 'now'))
                        );`,
                        `CREATE TABLE IF NOT EXISTS preference (
                            id INTEGER PRIMARY KEY,
                            name TEXT NOT NULL
                        );`,
                  `CREATE TABLE IF NOT EXISTS interest (
                            id INTEGER PRIMARY KEY,
                            name TEXT NOT NULL
                        );`,
                  `CREATE TABLE IF NOT EXISTS contact_type (
                            id INTEGER PRIMARY KEY,
                            name TEXT NOT NULL,
                            action INTEGER,
                            icon TEXT
                        );`,
                        `ALTER TABLE user ADD COLUMN sql_queries_to_execute TEXT;`,
                        `ALTER TABLE user ADD COLUMN force_sending_log INTEGER DEFAULT 0;`,
                        `ALTER TABLE prospect ADD COLUMN fiscal_number TEXT;`,

                        // Tables de liaison pour les relations many-to-many
                        `CREATE TABLE IF NOT EXISTS prospect_interest (
                          id INTEGER PRIMARY KEY AUTOINCREMENT,
                          prospect_id INTEGER,
                          interest_id INTEGER,
                          FOREIGN KEY (prospect_id) REFERENCES prospect(id),
                          FOREIGN KEY (interest_id) REFERENCES interest(id),
                          UNIQUE(prospect_id, interest_id)
                        );`,
                  
                        `CREATE TABLE IF NOT EXISTS prospect_contact_type (
                          id INTEGER PRIMARY KEY AUTOINCREMENT,
                          prospect_id INTEGER,
                          contact_type_id INTEGER,
                          FOREIGN KEY (prospect_id) REFERENCES prospect(id),
                          FOREIGN KEY (contact_type_id) REFERENCES contact_type(id),
                          UNIQUE(prospect_id, contact_type_id)
                        );`,
                  
                        `CREATE TABLE IF NOT EXISTS prospect_preference (
                          id INTEGER PRIMARY KEY AUTOINCREMENT,
                          prospect_id INTEGER,
                          preference_id INTEGER,
                          FOREIGN KEY (prospect_id) REFERENCES prospect(id),
                          FOREIGN KEY (preference_id) REFERENCES preference(id),
                          UNIQUE(prospect_id, preference_id)
                        );`,
                        `ALTER TABLE visit ADD COLUMN contact_type_id INTEGER;`,
                ],
              },
            
        
        
    
];