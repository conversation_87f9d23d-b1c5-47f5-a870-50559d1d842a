import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Alert<PERSON>ontroller, IonicModule, ToastController } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { Activity, ActivityType } from 'src/app/models/activity';
import { Expense, ExpenseType } from 'src/app/models/expense';
import { ActivityService } from 'src/app/services/activity.service';
import { CameraService } from 'src/app/services/camera.service';
import { DateService } from 'src/app/services/date.service';
import { ExpenseService } from 'src/app/services/expense.service';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { LoginService } from 'src/app/services/login-service.service';
import { ThemeService } from 'src/app/services/theme.service';
import { HeaderComponent } from '../../header/header.component';


@Component({
  selector: 'app-expense-list',
  templateUrl: './expense-list.component.html',
  styleUrls: ['./expense-list.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, ReactiveFormsModule,HeaderComponent]
})
export class ExpenseListComponent implements OnInit {
  //VARIABLES//
  isUpdate: boolean = false;
  expenseToEdit = new Expense()
  displayForm: boolean = false;
  expenseForm!: FormGroup;
  expenseId: number = -1
  activities: Activity[] = []
  expenses: Expense[] = [];
  types: ExpenseType[] = [];
  total: number = 0;
  lastDate: Date = new Date()
  firstDate: Date = new Date()
  translations: any = {};
  activityType: ActivityType[] = []
  expenseToAdd = new Expense();
  displayBottomAdd: boolean = true;
   destroy$ = new Subject<void>();
  attachementBase64: string = '';
  attachmentName: string = '';
  selectedType = new ExpenseType()
  isDarkTheme = false;
  maxFileSize = 1536000;
  constructor(private expenseService: ExpenseService,
    private formBuilder: FormBuilder,
    private alertCtrl: AlertController,
    private camera: CameraService,
    private activityService: ActivityService,
    private cdr: ChangeDetectorRef,
    private translationService: TranslationService,
    private dateService: DateService,
    private themeService: ThemeService,
    private LoginService : LoginService,
    private toastController: ToastController) { }
    ngOnDestroy() {
      this.destroy$.next();
      this.destroy$.complete();
    }
  ngOnInit() {
    this.loadTranslations('an.json');
    this.isUpdate = false;
    this.selectedType = new ExpenseType()
    this.displayForm = false;
    this.displayBottomAdd = true;
    this.initializeForm()
    const typeNameControl = this.expenseForm.get('typeName');
    const amountControl = this.expenseForm.get('amount');
    if (typeNameControl && amountControl) {
      typeNameControl.valueChanges.subscribe(selectedType => {
        if (selectedType) {
          amountControl.setValue(selectedType.amount);
        } else {
          amountControl.setValue(null);
        }
      });
    }
    this.getAllExpenses();
    this.getAllActivities();
    this.getAllActivityTypes()
    this.getAllExpenseTypes();

  }
// getting data from database 
getAllActivities(): void {
  this.activityService.getAllActivities()
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (activities) => {
        this.activities = activities;
      },
      error: error => {
        console.error('Erreur lors de la récupération des activités :', error);
        this.presentErrorToast('Échec du chargement des activités. Veuillez réessayer.');
      }
    });
}
getAllExpenseTypes() {
  this.expenseService.getAllExpenseTypes()
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: types => this.types = types,
      error: error => console.error('Error fetching types:', error)
    });
}
getAllExpenses(): void {
  this.expenseService.getAllExpenses()
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: ({ expenses, total }) => {
        this.expenses = expenses;
        this.total = total;

      },
      error: error => {
        console.error('Erreur lors de la récupération des dépenses :', error);
        this.presentErrorToast('Échec du chargement des dépenses. Veuillez réessayer.');
      }
    });
}
  getAllActivityTypes() {
    this.activityService.getAllActivityTypes()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: activityType => this.activityType = activityType,
        error: error => console.error('Error fetching types:', error)
      });
  }
  // filtre 
  getExpenseByDates() {
    const startDate = new Date(this.firstDate)
    const endDate = new Date(this.lastDate)
    this.expenseService.getExpenseByDates(startDate, endDate).subscribe({
      next: (expenses) => {
        this.expenses = expenses;
        this.total = this.calculateTotal(expenses);
      },
      error: (error) => console.error('Error fetching expenses by dates:', error)
    });
  }
  calculateTotal(expenses: Expense[]): number {
    return expenses.reduce((sum, expense) => sum + expense.montant, 0);
  }
  // formulaire 
  initializeForm() {
    try {
      this.expenseForm = this.formBuilder.group({
        expenseDate: new FormControl(new Date(), Validators.required),
        typeName: new FormControl('', Validators.required),
        amount: new FormControl(null, Validators.required),
        description: new FormControl(''),
        attachementBase64: new FormControl(''),  // Include this in your form
        attachmentName: new FormControl(''),     // Include this in your form
      });
    } catch (error) {
      console.error('Error setting up form:', error);
    }
  }
  
  typeChange(event: any) {
    this.selectedType = event.detail.value
    console.log(this.selectedType)
  }
  async presentErrorToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 3000,
      color: 'danger',
      position: 'top',
    });
    await toast.present();
  }
  
  async takePhotoForExpense() {
    try {
      const photo = await this.camera.takePhoto();
      if (photo) {
        this.attachementBase64 = photo.webviewPath!;
        this.attachmentName = photo.filepath;
      }
    } catch (error) {
      console.error('Erreur lors de la prise de la photo :', error);
      this.presentErrorToast('Impossible d’accéder à la caméra. Veuillez réessayer.');
    }
  }
  encodeImageFile(event: any) {
    const file = event.target.files[0];
    if (file.size <= this.maxFileSize) {
      const reader = new FileReader();
      reader.onload = () => {
        this.attachementBase64 = reader.result as string;
        this.attachmentName = file.name;

      };
      reader.readAsDataURL(file);
    } else {
      this.presentAlert(this.translate('VERY_LARGE_FILE_SIZE'), this.translate('PLEASE_CHOOSE_A_FILE_THAT_DOES_NOT_EXCEED_1_5_MB'));
    }

  }
  clearImageData() {
    this.attachementBase64 = ''; // Clear by assigning an empty string
    this.attachmentName = '';    // Clear by assigning an empty string

    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = ''; // Reset file input element
    }
  }
  presentAlert(header: string, message: string) {
    this.alertCtrl.create({
      header,
      message,
      buttons: [this.translate('OK')],
    }).then(alert => alert.present());
  }
  // buttons 
  cancel() {
    this.displayBottomAdd = true;
    this.displayForm = false;
    this.expenseForm.reset();
    if (this.isUpdate) { this.isUpdate = false }
  }
  addExpense() {
    this.displayBottomAdd = false;
    this.displayForm = true;
    this.cdr.detectChanges()
  }
  updateExpense(expense: Expense) {
    this.isUpdate = true;
    this.expenseToEdit = expense
    console.log(this.expenseToEdit)
    this.displayBottomAdd = false;
    this.displayForm = true;
    this.cdr.detectChanges()
    this.expenseForm.patchValue({
      expenseDate: this.dateService.formatDate(expense.expenseDate),
      typeName: this.types.find((x) => x.id === expense.expenseTypeId),
      amount: expense.montant,
      description: expense.description,
      attachementBase64: expense.attachementBase64,
      attachmentName: expense.attachmentName,
    });
  }
 
// display 
 getExpenseTypeNameFromListById(id: number): string {
    const type = this.types.find((x) => x.id === id);
    if (type) {
      return type.name
    } else {
      return ''
    }
  }
  getActivityTypeNameFromListById(id: number): string {
    const type = this.activityType.find((x) => x.id === id);
    if (type) {
      return type.name
    } else {
      return ''
    }
  }
   //LANGUE//
   translate(key: string): string {
    return this.translations[key] || key;
  }
  changeLanguage(lang: string) {
    this.loadTranslations(lang);
  }
  private loadTranslations(lang: string) {
    this.translationService.loadTranslations(lang).subscribe(
      (translations) => {
        this.translations = translations;

      },
      (error) => {
        console.error(`Error loading translations for ${lang}`, error);
      }
    );
  }
 
 
  // save 
  async saveExpense() {
    this.expenseToAdd = {
      id: new Date(this.expenseForm.value.expenseDate).getTime(),
      expenseTypeId: this.selectedType.id,
      description: this.expenseForm.value.description,
      activityId: 1, // valeur par defaut pour le moment
      montant: this.expenseForm.value.amount,
      status: "Nouveau",
      synchronized: 0,
      attachementBase64: this.attachementBase64,
      attachmentName: this.attachmentName,
      expenseDate: new Date(this.expenseForm.value.expenseDate).setHours(0, 0, 0, 0)
    };
    try {
      await this.expenseService.getExpense(this.expenseToAdd);
      this.expenses.unshift(this.expenseToAdd);
      this.calculateTotal(this.expenses);
      await this.getAllExpenses();
      this.expenseForm.reset();
      this.expenseToAdd = new Expense();
      this.displayForm = false;
      this.displayBottomAdd = true;
      this.clearImageData();
    } catch (error) {
      console.error("Error saving expense:", error);
    }
  }
  async saveUpdatedExpense() {
    if (this.expenseForm.invalid) {
      console.error('Form is invalid');
      return;
    }
    const formValue = this.expenseForm.value;
    console.log(formValue.expenseDate)
    const updatedExpense = {
      id: this.expenseToEdit.id,
      expenseDate: new Date(this.expenseForm.value.expenseDate).setHours(0, 0, 0, 0),
      expenseTypeId: this.selectedType.id,
      description: formValue.description,
      montant: formValue.amount,
      attachementBase64: this.attachementBase64 || this.expenseToEdit.attachementBase64,
      attachmentName: this.attachmentName || this.expenseToEdit.attachmentName
    };
    try {
      const result = await this.expenseService.updateExpense(updatedExpense);
      console.log('Expense updated:', result);
      await this.getAllExpenses();
    } catch (error) {
      console.error('Failed to update expense:', error);
    } finally {
      this.cancel();
    }
 
  }
  // delete 
  deleteExpense(id: number) {
    this.expenseService.deleteExpense(id).subscribe({
      next: () => {
        console.log('Expense deleted successfully');
        this.getAllExpenses()
        console.log("Delete ", this.expenses)
      },
      error: (error) => {
        console.error('Error deleting expense', error);
      }
    });
    this.clearImageData();
  }
  switchTheme(): void {
    this.themeService.switchTheme();
  }
  
  
    logout() {
      this.LoginService.logout();
    }
    onThemeChange(): void {
      this.themeService.switchTheme();
    }

}
